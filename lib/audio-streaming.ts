import { WebSocketMessage, AudioConfig, LanguageCode } from './types';

export class AudioStreamingClient {
  private audioContext: AudioContext | null = null;
  private audioSource: MediaStreamAudioSourceNode | null = null;
  private audioProcessor: ScriptProcessorNode | null = null;
  private audioStream: MediaStream | null = null;
  private audioSocket: WebSocket | null = null;
  private isStreaming = false;
  private packetsSent = 0;
  private totalBytes = 0;
  private currentVolume = 0;

  private onStatusChange?: (status: {
    micStatus: string;
    wsStatus: string;
    audioStatus: string;
    stats: {
      packetsSent: number;
      totalBytes: number;
      currentVolume: number;
      sampleRate: number;
      isResampling: boolean;
    };
  }) => void;

  private onMessage?: (message: any) => void;
  private onError?: (error: string) => void;

  constructor(
    onStatusChange?: (status: any) => void,
    onMessage?: (message: any) => void,
    onError?: (error: string) => void
  ) {
    this.onStatusChange = onStatusChange;
    this.onMessage = onMessage;
    this.onError = onError;
  }

  async startStreaming(
    srcLang: LanguageCode = 'en-US',
    targetLang: LanguageCode = 'en-US',
    roomId: string = 'default-room'
  ): Promise<void> {
    if (this.isStreaming) {
      throw new Error('Already streaming');
    }

    try {
      // Request microphone access
      this.updateStatus('Requesting microphone access...', 'warning', 'mic');
      
      const constraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      };

      this.audioStream = await navigator.mediaDevices.getUserMedia(constraints);
      this.updateStatus('Microphone access granted', 'success', 'mic');

      // Create AudioContext
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
      this.audioContext = new AudioContextClass();

      // Resume if suspended
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      // Create audio processing chain
      this.audioSource = this.audioContext.createMediaStreamSource(this.audioStream);
      this.audioProcessor = this.audioContext.createScriptProcessor(4096, 1, 1);

      this.audioSource.connect(this.audioProcessor);
      this.audioProcessor.connect(this.audioContext.destination);

      // Connect WebSocket
      this.updateStatus('Connecting to WebSocket...', 'warning', 'ws');
      const wsUrl = `ws://127.0.0.1:8001/ws/${srcLang}/${targetLang}`;
      this.audioSocket = new WebSocket(wsUrl);
      this.audioSocket.binaryType = 'arraybuffer';

      this.audioSocket.onopen = () => {
        this.updateStatus('WebSocket connected', 'success', 'ws');

        // Send join-room message
        const joinMessage: WebSocketMessage = {
          type: 'join-room',
          roomId,
          role: 'preacher',
          audioConfig: {
            sampleRate: this.audioContext!.sampleRate,
            channelCount: 1,
            encoding: 'pcm16',
          },
        };
        this.audioSocket!.send(JSON.stringify(joinMessage));
      };

      this.audioSocket.onclose = (event) => {
        this.updateStatus(`WebSocket closed (${event.code}: ${event.reason})`, 'error', 'ws');
      };

      this.audioSocket.onerror = () => {
        this.updateStatus('WebSocket error', 'error', 'ws');
      };

      this.audioSocket.onmessage = (event) => {
        if (typeof event.data === 'string') {
          const message = JSON.parse(event.data);
          this.onMessage?.(message);
        } else {
          // Received audio data
          this.onMessage?.({ type: 'audio', data: event.data });
        }
      };

      // Audio processing
      this.audioProcessor.onaudioprocess = (e) => {
        if (!this.isStreaming || this.audioSocket?.readyState !== WebSocket.OPEN) {
          return;
        }

        const input = e.inputBuffer.getChannelData(0);
        if (!input || input.length === 0) return;

        // Calculate volume
        let maxAmplitude = 0;
        for (let i = 0; i < input.length; i++) {
          maxAmplitude = Math.max(maxAmplitude, Math.abs(input[i]));
        }
        this.currentVolume = maxAmplitude;

        // Resample to 16kHz if needed
        let processedSamples = input;
        const currentSampleRate = this.audioContext!.sampleRate;
        const targetSampleRate = 16000;

        if (currentSampleRate !== targetSampleRate) {
          const ratio = currentSampleRate / targetSampleRate;
          const outputLength = Math.floor(input.length / ratio);
          const resampled = new Float32Array(outputLength);

          for (let i = 0; i < outputLength; i++) {
            const sourceIndex = Math.floor(i * ratio);
            resampled[i] = input[sourceIndex];
          }
          processedSamples = resampled;
        }

        // Convert to 16-bit PCM
        const pcm = new Int16Array(processedSamples.length);
        for (let i = 0; i < processedSamples.length; i++) {
          let s = Math.max(-1, Math.min(1, processedSamples[i]));
          pcm[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
        }

        // Send to server
        try {
          this.audioSocket!.send(pcm.buffer);
          this.packetsSent++;
          this.totalBytes += pcm.buffer.byteLength;
          this.updateStats();
        } catch (error) {
          this.onError?.(`Error sending audio: ${error}`);
        }
      };

      this.isStreaming = true;
      this.updateStatus('Audio processing active', 'success', 'audio');
    } catch (error) {
      this.onError?.(`Error starting audio: ${error}`);
      this.stopStreaming();
      throw error;
    }
  }

  stopStreaming(): void {
    this.isStreaming = false;

    if (this.audioProcessor) {
      this.audioProcessor.disconnect();
      this.audioProcessor.onaudioprocess = null;
      this.audioProcessor = null;
    }

    if (this.audioSource) {
      this.audioSource.disconnect();
      this.audioSource = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    if (this.audioStream) {
      this.audioStream.getTracks().forEach((track) => track.stop());
      this.audioStream = null;
    }

    if (this.audioSocket) {
      this.audioSocket.close();
      this.audioSocket = null;
    }

    this.updateStatus('Microphone: Not accessed', 'info', 'mic');
    this.updateStatus('WebSocket: Not connected', 'info', 'ws');
    this.updateStatus('Audio Processing: Stopped', 'info', 'audio');
  }

  private updateStatus(message: string, type: string, component: string): void {
    const status = {
      micStatus: component === 'mic' ? message : 'Microphone: Not accessed',
      wsStatus: component === 'ws' ? message : 'WebSocket: Not connected',
      audioStatus: component === 'audio' ? message : 'Audio Processing: Stopped',
      stats: {
        packetsSent: this.packetsSent,
        totalBytes: this.totalBytes,
        currentVolume: this.currentVolume,
        sampleRate: this.audioContext?.sampleRate || 0,
        isResampling: this.audioContext ? this.audioContext.sampleRate !== 16000 : false,
      },
    };
    this.onStatusChange?.(status);
  }

  private updateStats(): void {
    this.updateStatus('', '', '');
  }

  getStats() {
    return {
      packetsSent: this.packetsSent,
      totalBytes: this.totalBytes,
      currentVolume: this.currentVolume,
      sampleRate: this.audioContext?.sampleRate || 0,
      isResampling: this.audioContext ? this.audioContext.sampleRate !== 16000 : false,
    };
  }
}
