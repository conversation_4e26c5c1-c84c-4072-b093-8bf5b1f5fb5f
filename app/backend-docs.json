{"openapi": "3.1.0", "info": {"title": "Omnispeak Backend", "description": "Live video translation service with real-time speech processing", "version": "0.2.0"}, "paths": {"/auth/register": {"post": {"tags": ["Authentication"], "summary": "Register Host", "description": "Register a new host user", "operationId": "register_host_auth_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRegister"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/login": {"post": {"tags": ["Authentication"], "summary": "<PERSON>gin <PERSON>", "description": "Login host user", "operationId": "login_host_auth_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLogin"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/guest": {"post": {"tags": ["Authentication"], "summary": "Join <PERSON>", "description": "Create guest session", "operationId": "join_as_guest_auth_guest_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Guest<PERSON>oin"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/logout": {"post": {"tags": ["Authentication"], "summary": "Logout", "description": "Logout user", "operationId": "logout_auth_logout_post", "parameters": [{"name": "session_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Token"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/me": {"get": {"tags": ["Authentication"], "summary": "Get Current User Profile", "description": "Get current host user profile", "operationId": "get_current_user_profile_auth_me_get", "parameters": [{"name": "session_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Token"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfile"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/profile": {"put": {"tags": ["Authentication"], "summary": "Update Profile", "description": "Update host user profile", "operationId": "update_profile_auth_profile_put", "parameters": [{"name": "session_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Token"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfile"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/voice-sample": {"post": {"tags": ["Authentication"], "summary": "Upload Voice Sample", "description": "Upload voice sample for host", "operationId": "upload_voice_sample_auth_voice_sample_post", "parameters": [{"name": "session_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Token"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_voice_sample_auth_voice_sample_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/status": {"get": {"tags": ["Authentication"], "summary": "Auth Status", "description": "Get current authentication status", "operationId": "auth_status_auth_status_get", "parameters": [{"name": "session_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Token"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/meetings/auth/add_user": {"post": {"tags": ["Meetings"], "summary": "Add User Api", "operationId": "add_user_api_meetings_auth_add_user_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/meetings/auth/speaker_login": {"post": {"tags": ["Meetings"], "summary": "Speaker <PERSON><PERSON>", "operationId": "speaker_login_meetings_auth_speaker_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SpeakerLoginRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SpeakerLoginResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/meetings/rooms/list_rooms": {"get": {"tags": ["Meetings"], "summary": "List Rooms", "operationId": "list_rooms_meetings_rooms_list_rooms_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/meetings/rooms/metadata/{room_id}": {"get": {"tags": ["Meetings"], "summary": "Get Room Metadata", "operationId": "get_room_metadata_meetings_rooms_metadata__room_id__get", "parameters": [{"name": "room_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Room Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/meetings/rooms/join_room": {"post": {"tags": ["Meetings"], "summary": "Join Room", "operationId": "join_room_meetings_rooms_join_room_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoomJoinRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/meetings/rooms/leave_room": {"post": {"tags": ["Meetings"], "summary": "Leave Room", "operationId": "leave_room_meetings_rooms_leave_room_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoomLeaveRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/meetings/rooms/create": {"post": {"tags": ["Meetings"], "summary": "Create Room", "operationId": "create_room_meetings_rooms_create_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoomCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/meetings/rooms/delete": {"post": {"tags": ["Meetings"], "summary": "Delete Room", "operationId": "delete_room_meetings_rooms_delete_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoomDeleteRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/meetings/rooms/set_lang_limit": {"post": {"tags": ["Meetings"], "summary": "<PERSON>", "operationId": "set_lang_limit_meetings_rooms_set_lang_limit_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoomSetLangLimitRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/meetings/rooms/chat": {"get": {"tags": ["Meetings"], "summary": "Get Room Chat", "operationId": "get_room_chat_meetings_rooms_chat_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}, "post": {"tags": ["Meetings"], "summary": "Post Room Chat", "operationId": "post_room_chat_meetings_rooms_chat_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoomChatRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/health": {"get": {"summary": "Health Check", "description": "Health check endpoint", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/chat": {"post": {"summary": "Post Chat Message", "description": "Post a chat message", "operationId": "post_chat_message_chat_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageInc"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/pipeline/stats": {"get": {"summary": "Get Pipeline Stats", "description": "Get statistics for all active pipelines", "operationId": "get_pipeline_stats_api_pipeline_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/pipeline/sessions": {"get": {"summary": "Get Active Sessions", "description": "Get list of active translation sessions", "operationId": "get_active_sessions_api_pipeline_sessions_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"AuthResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "user": {"anyOf": [{"$ref": "#/components/schemas/UserProfile"}, {"type": "null"}]}, "session_token": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Token"}}, "type": "object", "required": ["success", "message"], "title": "AuthResponse", "description": "Authentication response"}, "Body_upload_voice_sample_auth_voice_sample_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_voice_sample_auth_voice_sample_post"}, "ChatMessageInc": {"properties": {"text": {"type": "string", "title": "Text"}, "room_id": {"type": "string", "title": "Room Id"}}, "type": "object", "required": ["text", "room_id"], "title": "ChatMessageInc"}, "GuestJoin": {"properties": {"guest_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Guest Name"}}, "type": "object", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Guest join request"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "RoomChatRequest": {"properties": {"room_id": {"type": "integer", "title": "Room Id"}, "jwt": {"type": "string", "title": "Jwt"}, "viewer_id": {"type": "string", "title": "Viewer Id"}, "viewer_name": {"type": "string", "title": "Viewer Name"}, "text": {"type": "string", "title": "Text"}}, "type": "object", "required": ["room_id"], "title": "RoomChatRequest"}, "RoomCreateRequest": {"properties": {"title": {"type": "string", "title": "Title"}, "description": {"type": "string", "title": "Description", "default": ""}, "is_public": {"type": "boolean", "title": "Is Public", "default": true}, "host_language": {"type": "string", "title": "Host Language", "default": "en"}, "jwt": {"type": "string", "title": "Jwt"}}, "type": "object", "required": ["title", "jwt"], "title": "RoomCreateRequest"}, "RoomDeleteRequest": {"properties": {"room_id": {"type": "integer", "title": "Room Id"}, "jwt": {"type": "string", "title": "Jwt"}}, "type": "object", "required": ["room_id", "jwt"], "title": "RoomDeleteRequest"}, "RoomJoinRequest": {"properties": {"room_id": {"type": "integer", "title": "Room Id"}, "viewer_id": {"type": "string", "title": "Viewer Id"}, "viewer_name": {"type": "string", "title": "Viewer Name"}}, "type": "object", "required": ["room_id", "viewer_id"], "title": "RoomJoinRequest"}, "RoomLeaveRequest": {"properties": {"room_id": {"type": "integer", "title": "Room Id"}, "viewer_id": {"type": "string", "title": "Viewer Id"}}, "type": "object", "required": ["room_id", "viewer_id"], "title": "RoomLeaveRequest"}, "RoomSetLangLimitRequest": {"properties": {"room_id": {"type": "integer", "title": "Room Id"}, "jwt": {"type": "string", "title": "Jwt"}, "lang_limit": {"type": "integer", "title": "Lang Limit"}}, "type": "object", "required": ["room_id", "jwt", "lang_limit"], "title": "RoomSetLangLimitRequest"}, "SpeakerLoginRequest": {"properties": {"email": {"type": "string", "title": "Email"}, "password": {"type": "string", "title": "Password"}}, "type": "object", "required": ["email", "password"], "title": "SpeakerLoginRequest"}, "SpeakerLoginResponse": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "token_type": {"type": "string", "title": "Token Type", "default": "bearer"}, "error": {"type": "string", "title": "Error"}}, "type": "object", "required": ["access_token"], "title": "SpeakerLoginResponse"}, "UserCreateRequest": {"properties": {"email": {"type": "string", "title": "Email"}, "password": {"type": "string", "title": "Password"}, "jwt": {"type": "string", "title": "Jwt"}}, "type": "object", "required": ["email", "password", "jwt"], "title": "UserCreateRequest"}, "UserLogin": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "password": {"type": "string", "title": "Password"}}, "type": "object", "required": ["email", "password"], "title": "UserLogin", "description": "User login request"}, "UserProfile": {"properties": {"id": {"type": "integer", "title": "Id"}, "email": {"type": "string", "title": "Email"}, "full_name": {"type": "string", "title": "Full Name"}, "preferred_language": {"type": "string", "title": "Preferred Language"}, "original_language": {"type": "string", "title": "Original Language"}, "voice_sample_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Voice Sample Url"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "email", "full_name", "preferred_language", "original_language", "created_at"], "title": "UserProfile", "description": "User profile response"}, "UserProfileUpdate": {"properties": {"full_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Full Name"}, "preferred_language": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preferred Language"}, "original_language": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Original Language"}}, "type": "object", "title": "UserProfileUpdate", "description": "User profile update request"}, "UserRegister": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "password": {"type": "string", "title": "Password"}, "full_name": {"type": "string", "title": "Full Name"}, "preferred_language": {"type": "string", "title": "Preferred Language", "default": "en"}, "original_language": {"type": "string", "title": "Original Language", "default": "en"}}, "type": "object", "required": ["email", "password", "full_name"], "title": "UserRegister", "description": "User registration request"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}