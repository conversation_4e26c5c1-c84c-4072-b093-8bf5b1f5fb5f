"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Video,
  VideoOff,
  Mic,
  MicOff,
  MessageSquare,
  Users,
  Phone,
  PhoneOff,
  Send,
  Maximize2,
  Languages,
  X,
} from "lucide-react";

export default function StreamingInterface() {
  const [isVideoOn, setIsVideoOn] = useState(false);
  const [isMicOn, setIsMicOn] = useState(false);
  const [isCallActive, setIsCallActive] = useState(true);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<"chat" | "participants">("chat");
  const [callDuration, setCallDuration] = useState(449); // 07:29
  const [chatMessage, setChatMessage] = useState("");
  const [chatMessages, setChatMessages] = useState([
    {
      id: 1,
      user: "<PERSON>",
      message: "Great presentation!",
      time: "2:30 PM",
      avatar: "/placeholder-user.jpg",
    },
    {
      id: 2,
      user: "Jane Smith",
      message: "Can you share the slides?",
      time: "2:31 PM",
      avatar: "/placeholder-user.jpg",
    },
  ]);
  const [participants] = useState([
    {
      id: 1,
      name: "Speaker Name",
      role: "Host",
      isMuted: false,
      isVideoOn: true,
      avatar: "/placeholder-user.jpg",
    },
    {
      id: 2,
      name: "John Doe",
      role: "Participant",
      isMuted: true,
      isVideoOn: true,
      avatar: "/placeholder-user.jpg",
    },
  ]);

  const videoRef = useRef<HTMLVideoElement>(null);
  const videoStreamRef = useRef<MediaStream | null>(null);
  const audioStreamRef = useRef<MediaStream | null>(null);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isCallActive) {
      interval = setInterval(() => {
        setCallDuration((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isCallActive]);

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  const toggleVideo = async () => {
    try {
      if (!isVideoOn) {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: { width: 1280, height: 720 },
          audio: false,
        });
        videoStreamRef.current = stream;
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          videoRef.current.play().catch(console.error);
        }
      } else {
        if (videoStreamRef.current) {
          videoStreamRef.current.getTracks().forEach((track) => track.stop());
          videoStreamRef.current = null;
          if (videoRef.current) {
            videoRef.current.srcObject = null;
          }
        }
      }
      setIsVideoOn(!isVideoOn);
    } catch (error) {
      console.error("Error accessing camera:", error);
      alert("Could not access camera. Please check permissions.");
    }
  };

  const toggleMic = async () => {
    try {
      if (!isMicOn) {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: true,
          video: false,
        });
        audioStreamRef.current = stream;
      } else {
        if (audioStreamRef.current) {
          audioStreamRef.current.getTracks().forEach((track) => track.stop());
          audioStreamRef.current = null;
        }
      }
      setIsMicOn(!isMicOn);
    } catch (error) {
      console.error("Error accessing microphone:", error);
      alert("Could not access microphone. Please check permissions.");
    }
  };

  const endCall = () => {
    setIsCallActive(false);
    if (videoStreamRef.current) {
      videoStreamRef.current.getTracks().forEach((track) => track.stop());
      videoStreamRef.current = null;
    }
    if (audioStreamRef.current) {
      audioStreamRef.current.getTracks().forEach((track) => track.stop());
      audioStreamRef.current = null;
    }
    setIsVideoOn(false);
    setIsMicOn(false);
  };

  const sendMessage = () => {
    if (chatMessage.trim()) {
      const newMessage = {
        id: chatMessages.length + 1,
        user: "You",
        message: chatMessage,
        avatar: "/placeholder-user.jpg",
        time: new Date().toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        }),
      };
      setChatMessages([...chatMessages, newMessage]);
      setChatMessage("");
    }
  };

  return (
    <div className="h-screen bg-black flex flex-col relative overflow-hidden">
      {/* Header */}
      <header className="bg-black border-b border-gray-800 px-6 py-4 flex items-center justify-between relative z-10">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
            <span className="text-black text-sm font-bold">L</span>
          </div>
          <h1 className="text-white text-xl font-medium">Logos Live</h1>
        </div>

        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 bg-gray-800 rounded-full px-3 py-1">
            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
            <span className="text-white text-sm">0</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-gray-800 rounded-full w-8 h-8 p-0"
          >
            <span className="text-lg">🌐</span>
          </Button>
          <Avatar className="w-8 h-8">
            <AvatarImage src="/placeholder-user.jpg" />
            <AvatarFallback className="bg-gray-700 text-white text-sm">
              AA
            </AvatarFallback>
          </Avatar>
        </div>
      </header>

      {/* Main Video Area */}
      <div className="flex-1 relative">
        {/* Speaker Info Overlay - Top Left */}
        <div className="absolute top-6 left-6 bg-black/70 backdrop-blur-sm rounded-xl px-4 py-3 z-20">
          <h2 className="text-white text-lg font-medium">Speaker Name</h2>
          <div className="flex items-center gap-4 mt-1">
            <span className="text-gray-300 text-sm">
              Call: {formatDuration(callDuration)}
            </span>
          </div>
        </div>

        {/* Video Content */}
        <div className="w-full h-full relative">
          {isVideoOn ? (
            <video
              ref={videoRef}
              autoPlay
              muted
              playsInline
              className="w-full h-full object-cover"
              onLoadedMetadata={() => {
                videoRef.current?.play().catch(console.error);
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-black">
              <div className="text-center">
                <Avatar className="w-40 h-40 mx-auto mb-6">
                  <AvatarImage src="/placeholder-user.jpg" />
                  <AvatarFallback className="text-6xl bg-gray-800 text-white">
                    SN
                  </AvatarFallback>
                </Avatar>
                <h2 className="text-white text-2xl font-medium mb-2">
                  Speaker Name
                </h2>
                <p className="text-gray-400">Camera is off</p>
              </div>
            </div>
          )}
        </div>

        {/* Floating Controls - Left Side */}
        <div className="absolute bottom-6 left-6 flex flex-col gap-3 z-20">
          <Button
            size="sm"
            variant="secondary"
            className="rounded-lg w-10 h-10 p-0 bg-black/70 hover:bg-black/80 border border-gray-600"
          >
            <Maximize2 className="w-4 h-4 text-white" />
          </Button>
          <Button
            size="sm"
            variant="secondary"
            className="rounded-lg w-10 h-10 p-0 bg-black/70 hover:bg-black/80 border border-gray-600"
          >
            <Languages className="w-4 h-4 text-white" />
          </Button>
        </div>

        {/* Bottom Controls */}
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20">
          <div className="flex items-center gap-4">
            {/* Video Toggle */}
            <Button
              onClick={toggleVideo}
              className={`rounded-full w-14 h-14 p-0 ${
                isVideoOn
                  ? "bg-green-500 hover:bg-green-600"
                  : "bg-gray-600 hover:bg-gray-700"
              }`}
            >
              {isVideoOn ? (
                <Video className="w-6 h-6 text-white" />
              ) : (
                <VideoOff className="w-6 h-6 text-white" />
              )}
            </Button>

            {/* Mic Toggle */}
            <Button
              onClick={toggleMic}
              className={`rounded-full w-14 h-14 p-0 ${
                isMicOn
                  ? "bg-green-500 hover:bg-green-600"
                  : "bg-gray-600 hover:bg-gray-700"
              }`}
            >
              {isMicOn ? (
                <Mic className="w-6 h-6 text-white" />
              ) : (
                <MicOff className="w-6 h-6 text-white" />
              )}
            </Button>

            {/* End Call */}
            <Button
              onClick={endCall}
              className="rounded-full w-14 h-14 p-0 bg-red-500 hover:bg-red-600"
            >
              <PhoneOff className="w-6 h-6 text-white" />
            </Button>
          </div>
        </div>

        {/* Chat/Participants Button - Bottom Right */}
        <div className="absolute bottom-6 right-6 z-20">
          <Button
            onClick={() => setIsChatOpen(!isChatOpen)}
            className="rounded-full w-14 h-14 p-0 bg-blue-500 hover:bg-blue-600"
          >
            <MessageSquare className="w-6 h-6 text-white" />
          </Button>
        </div>
      </div>

      {/* Chat/Participants Modal */}
      {isChatOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
          <Card className="w-96 h-[600px] bg-white rounded-xl shadow-2xl flex flex-col">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <div className="flex gap-1">
                <Button
                  variant={activeTab === "chat" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setActiveTab("chat")}
                  className="rounded-lg"
                >
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Chat
                </Button>
                <Button
                  variant={activeTab === "participants" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setActiveTab("participants")}
                  className="rounded-lg"
                >
                  <Users className="w-4 h-4 mr-2" />
                  People ({participants.length})
                </Button>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsChatOpen(false)}
                className="rounded-full w-8 h-8 p-0"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Modal Content */}
            <div className="flex-1 overflow-hidden">
              {activeTab === "chat" ? (
                <div className="flex flex-col h-full">
                  {/* Chat Messages */}
                  <div className="flex-1 p-4 overflow-y-auto space-y-4">
                    {chatMessages.map((msg) => (
                      <div key={msg.id} className="flex gap-3">
                        <Avatar className="w-8 h-8 flex-shrink-0">
                          <AvatarImage src={msg.avatar} />
                          <AvatarFallback className="text-xs bg-gray-200">
                            {msg.user
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium text-gray-900 truncate">
                              {msg.user}
                            </span>
                            <span className="text-xs text-gray-500">
                              {msg.time}
                            </span>
                          </div>
                          <p className="text-sm text-gray-700 break-words">
                            {msg.message}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Chat Input */}
                  <div className="p-4 border-t">
                    <div className="flex gap-2">
                      <Input
                        placeholder="Type a message..."
                        value={chatMessage}
                        onChange={(e) => setChatMessage(e.target.value)}
                        onKeyPress={(e) => e.key === "Enter" && sendMessage()}
                        className="flex-1 rounded-lg"
                      />
                      <Button onClick={sendMessage} className="rounded-lg px-3">
                        <Send className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                /* Participants List */
                <div className="p-4 space-y-3">
                  {participants.map((participant) => (
                    <div
                      key={participant.id}
                      className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50"
                    >
                      <div className="relative">
                        <Avatar className="w-10 h-10">
                          <AvatarImage src={participant.avatar} />
                          <AvatarFallback className="bg-gray-200">
                            {participant.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        {participant.role === "Host" && (
                          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs">👑</span>
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {participant.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {participant.role}
                        </p>
                      </div>
                      <div className="flex items-center gap-1">
                        {participant.isMuted ? (
                          <MicOff className="w-4 h-4 text-red-500" />
                        ) : (
                          <Mic className="w-4 h-4 text-green-500" />
                        )}
                        {participant.isVideoOn ? (
                          <Video className="w-4 h-4 text-green-500" />
                        ) : (
                          <VideoOff className="w-4 h-4 text-red-500" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Card>
        </div>
      )}

      {/* Session Info - Bottom Left */}
      <div className="absolute bottom-4 left-4 text-white/70 text-sm z-10">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-green-400 rounded-full"></div>
          <span>{formatDuration(callDuration)}</span>
          <span>zth-iqpb-bcs</span>
        </div>
      </div>
    </div>
  );
}
