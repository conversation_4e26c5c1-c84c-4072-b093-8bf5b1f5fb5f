@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: #f8fafc;
  --foreground: #1f2937;
  --card: #ffffff;
  --card-foreground: #374151;
  --popover: #ffffff;
  --popover-foreground: #1f2937;
  --primary: #374151;
  --primary-foreground: #ffffff;
  --secondary: #6366f1;
  --secondary-foreground: #ffffff;
  --muted: #d1d5db;
  --muted-foreground: #6b7280;
  --accent: #6366f1;
  --accent-foreground: #ffffff;
  --destructive: #be123c;
  --destructive-foreground: #ffffff;
  --border: #e5e7eb;
  --input: #ffffff;
  --ring: rgba(99, 102, 241, 0.5);
  --chart-1: #ecfdf5;
  --chart-2: #d1fae5;
  --chart-3: #a7f3d0;
  --chart-4: #6ee7b7;
  --chart-5: #34d399;
  --radius: 0.5rem;
  --sidebar: #ffffff;
  --sidebar-foreground: #1f2937;
  --sidebar-primary: #f8fafc;
  --sidebar-primary-foreground: #374151;
  --sidebar-accent: #6366f1;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #e5e7eb;
  --sidebar-ring: rgba(99, 102, 241, 0.5);
}

.dark {
  --background: #0f172a;
  --foreground: #f1f5f9;
  --card: #1e293b;
  --card-foreground: #f1f5f9;
  --popover: #1e293b;
  --popover-foreground: #f1f5f9;
  --primary: #f1f5f9;
  --primary-foreground: #0f172a;
  --secondary: #4f46e5;
  --secondary-foreground: #f1f5f9;
  --muted: #334155;
  --muted-foreground: #94a3b8;
  --accent: #4f46e5;
  --accent-foreground: #f1f5f9;
  --destructive: #dc2626;
  --destructive-foreground: #f1f5f9;
  --border: #334155;
  --input: #1e293b;
  --ring: rgba(79, 70, 229, 0.5);
  --chart-1: #1e293b;
  --chart-2: #334155;
  --chart-3: #475569;
  --chart-4: #64748b;
  --chart-5: #94a3b8;
  --sidebar: #1e293b;
  --sidebar-foreground: #f1f5f9;
  --sidebar-primary: #4f46e5;
  --sidebar-primary-foreground: #f1f5f9;
  --sidebar-accent: #334155;
  --sidebar-accent-foreground: #f1f5f9;
  --sidebar-border: #334155;
  --sidebar-ring: rgba(79, 70, 229, 0.5);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
